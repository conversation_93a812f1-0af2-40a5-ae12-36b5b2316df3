#!/usr/bin/env python3
"""
Test script for geometry validation and repair functionality
"""

import os
import sys
import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon
from shapely.validation import explain_validity, make_valid
from shapely.ops import unary_union

def test_geometry_repair_methods():
    """Test various geometry repair methods"""
    print("=== Testing Geometry Repair Methods ===\n")
    
    # Create some invalid geometries for testing
    test_geometries = []
    
    # Self-intersecting polygon (bowtie)
    bowtie_coords = [(0, 0), (2, 2), (2, 0), (0, 2), (0, 0)]
    bowtie = Polygon(bowtie_coords)
    test_geometries.append(("Self-intersecting (bowtie)", bowtie))
    
    # Polygon with duplicate points
    duplicate_coords = [(0, 0), (1, 0), (1, 0), (1, 1), (0, 1), (0, 0)]
    duplicate_poly = Polygon(duplicate_coords)
    test_geometries.append(("Duplicate points", duplicate_poly))
    
    print("Testing repair methods on invalid geometries:")
    
    for name, geom in test_geometries:
        print(f"\n{name}:")
        print(f"  Valid: {geom.is_valid}")
        if not geom.is_valid:
            print(f"  Issue: {explain_validity(geom)}")
            
            # Test make_valid
            try:
                repaired = make_valid(geom)
                print(f"  make_valid(): Valid={repaired.is_valid}, Type={type(repaired).__name__}")
            except Exception as e:
                print(f"  make_valid() failed: {e}")
            
            # Test buffer(0)
            try:
                repaired = geom.buffer(0)
                print(f"  buffer(0): Valid={repaired.is_valid}, Type={type(repaired).__name__}")
            except Exception as e:
                print(f"  buffer(0) failed: {e}")

def test_shapefile_geometry_issues():
    """Test the actual shapefile for geometry issues"""
    shapefile_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\Polygon_ARE_C_dengan_holes_fixed_UPDATED.shp"
    
    print("\n=== Testing Actual Shapefile ===\n")
    
    if not os.path.exists(shapefile_path):
        print(f"Shapefile not found: {shapefile_path}")
        return None
    
    try:
        print("Loading shapefile...")
        gdf = gpd.read_file(shapefile_path)
        print(f"Loaded {len(gdf)} polygons")
        
        # Check for invalid geometries
        invalid_mask = ~gdf.geometry.is_valid
        invalid_count = invalid_mask.sum()
        
        print(f"Invalid geometries found: {invalid_count}")
        
        if invalid_count > 0:
            print("\nAnalyzing invalid geometries:")
            invalid_gdf = gdf[invalid_mask]
            
            # Show first few invalid geometries
            for idx, row in invalid_gdf.head(5).iterrows():
                geom = row.geometry
                validity_issue = explain_validity(geom)
                print(f"  Index {idx}: {validity_issue}")
            
            if invalid_count > 5:
                print(f"  ... and {invalid_count - 5} more invalid geometries")
        
        # Test specific field that was causing issues
        if 'SUBDIVISI' in gdf.columns:
            print(f"\nTesting SUBDIVISI field:")
            subdivisi_values = gdf['SUBDIVISI'].unique()
            print(f"Unique SUBDIVISI values: {list(subdivisi_values)}")
            
            # Test the specific values that were causing issues
            test_values = ['SUB DIVISI ARE C 1', 'SUB DIVISI ARE C 2', 'SUB DIVISI ARE C 3']
            available_values = [v for v in test_values if v in subdivisi_values]
            
            if available_values:
                print(f"Testing dissolve with values: {available_values}")
                
                # Filter to test values
                mask = gdf['SUBDIVISI'].isin(available_values)
                test_gdf = gdf[mask].copy()
                print(f"Filtered to {len(test_gdf)} polygons")
                
                # Check validity of filtered polygons
                invalid_in_test = (~test_gdf.geometry.is_valid).sum()
                print(f"Invalid geometries in test set: {invalid_in_test}")
                
                if invalid_in_test > 0:
                    print("Attempting to repair invalid geometries...")
                    repaired_count = 0
                    
                    for idx, row in test_gdf.iterrows():
                        if not row.geometry.is_valid:
                            try:
                                repaired = make_valid(row.geometry)
                                if repaired.is_valid:
                                    test_gdf.at[idx, 'geometry'] = repaired
                                    repaired_count += 1
                            except Exception as e:
                                print(f"    Failed to repair geometry at index {idx}: {e}")
                    
                    print(f"Repaired {repaired_count} geometries")
                
                # Try dissolve operation
                try:
                    print("Attempting dissolve operation...")
                    dissolved = test_gdf.dissolve(by='SUBDIVISI', as_index=False)
                    print(f"✓ Dissolve successful! Created {len(dissolved)} features")
                    
                    for _, row in dissolved.iterrows():
                        value = row['SUBDIVISI']
                        geom_type = type(row.geometry).__name__
                        area = row.geometry.area
                        print(f"  {value}: {geom_type}, Area = {area:.2f}")
                        
                except Exception as e:
                    print(f"✗ Dissolve failed: {e}")
                    
                    # Try alternative method
                    print("Trying alternative dissolve method...")
                    try:
                        dissolved_list = []
                        for value in available_values:
                            value_gdf = test_gdf[test_gdf['SUBDIVISI'] == value]
                            if len(value_gdf) > 0:
                                geometries = value_gdf.geometry.tolist()
                                merged = unary_union(geometries)
                                
                                new_row = value_gdf.iloc[0].copy()
                                new_row.geometry = merged
                                dissolved_list.append(new_row)
                        
                        if dissolved_list:
                            dissolved = gpd.GeoDataFrame(dissolved_list, crs=test_gdf.crs)
                            print(f"✓ Alternative dissolve successful! Created {len(dissolved)} features")
                        else:
                            print("✗ Alternative dissolve also failed")
                            
                    except Exception as alt_e:
                        print(f"✗ Alternative dissolve failed: {alt_e}")
            else:
                print("Test values not found in SUBDIVISI field")
        else:
            print("SUBDIVISI field not found in shapefile")
        
        return gdf
        
    except Exception as e:
        print(f"Error loading or processing shapefile: {e}")
        return None

def main():
    """Main test function"""
    print("Geometry Repair and Dissolve Test")
    print("=" * 50)
    
    # Test repair methods
    test_geometry_repair_methods()
    
    # Test actual shapefile
    gdf = test_shapefile_geometry_issues()
    
    print("\n" + "=" * 50)
    if gdf is not None:
        print("✓ Shapefile testing completed")
        print("The enhanced polygon dissolve app should now handle geometry issues better.")
    else:
        print("✗ Shapefile testing failed")
        print("Please check the shapefile path and ensure GeoPandas is installed.")

if __name__ == "__main__":
    main()
