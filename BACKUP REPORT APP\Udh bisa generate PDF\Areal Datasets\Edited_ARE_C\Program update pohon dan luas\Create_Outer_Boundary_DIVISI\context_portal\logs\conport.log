2025-08-03 11:36:31,034 - INFO - context_portal_mcp.main - File logging configured to: D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Create_Outer_Boundary_DIVISI\context_portal\./logs/conport.log
2025-08-03 11:36:31,034 - INFO - context_portal_mcp.main - Parsed CLI args: Namespace(host='127.0.0.1', port=8000, workspace_id='D:\\Gawean Rebinmas\\Tree Counting Project\\Training Tree Counter Sawit Current\\BACKUP REPORT APP\\Udh bisa generate PDF\\Areal Datasets\\Edited_ARE_C\\Program update pohon dan luas\\Create_Outer_Boundary_DIVISI', mode='stdio', log_file='./logs/conport.log', log_level='INFO')
2025-08-03 11:36:31,035 - INFO - context_portal_mcp.main - Starting ConPort in STDIO mode using FastMCP for initial CLI arg workspace_id: D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Create_Outer_Boundary_DIVISI
2025-08-03 11:36:31,035 - INFO - context_portal_mcp.main - Pre-warming database connection for workspace: D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Create_Outer_Boundary_DIVISI
2025-08-03 11:36:31,036 - INFO - context_portal_mcp.db.database - Alembic.ini not found. Creating at D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Create_Outer_Boundary_DIVISI\context_portal\alembic.ini
2025-08-03 11:36:31,038 - INFO - context_portal_mcp.db.database - Alembic env.py not found. Creating at D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Create_Outer_Boundary_DIVISI\context_portal\alembic\env.py
2025-08-03 11:36:31,039 - INFO - context_portal_mcp.db.database - Initial schema not found. Creating at D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Create_Outer_Boundary_DIVISI\context_portal\alembic\versions\2025_06_17_initial_schema.py
2025-08-03 11:36:31,040 - ERROR - context_portal_mcp.db.database - Failed to create initial schema at D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Create_Outer_Boundary_DIVISI\context_portal\alembic\versions\2025_06_17_initial_schema.py: [Errno 2] No such file or directory: 'D:\\Gawean Rebinmas\\Tree Counting Project\\Training Tree Counter Sawit Current\\BACKUP REPORT APP\\Udh bisa generate PDF\\Areal Datasets\\Edited_ARE_C\\Program update pohon dan luas\\Create_Outer_Boundary_DIVISI\\context_portal\\alembic\\versions\\2025_06_17_initial_schema.py'
2025-08-03 11:36:31,040 - ERROR - context_portal_mcp.main - Failed to pre-warm database connection for workspace 'D:\Gawean Rebinmas\Tree Counting Project\Training Tree Counter Sawit Current\BACKUP REPORT APP\Udh bisa generate PDF\Areal Datasets\Edited_ARE_C\Program update pohon dan luas\Create_Outer_Boundary_DIVISI': Could not create initial schema: [Errno 2] No such file or directory: 'D:\\Gawean Rebinmas\\Tree Counting Project\\Training Tree Counter Sawit Current\\BACKUP REPORT APP\\Udh bisa generate PDF\\Areal Datasets\\Edited_ARE_C\\Program update pohon dan luas\\Create_Outer_Boundary_DIVISI\\context_portal\\alembic\\versions\\2025_06_17_initial_schema.py'
