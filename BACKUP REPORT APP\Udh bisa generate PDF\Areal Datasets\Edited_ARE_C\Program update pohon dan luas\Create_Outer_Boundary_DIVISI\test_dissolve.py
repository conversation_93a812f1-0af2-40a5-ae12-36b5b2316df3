#!/usr/bin/env python3
"""
Test script for polygon dissolve functionality
"""

import os
import sys
import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon

def test_shapefile_loading():
    """Test loading the target shapefile"""
    shapefile_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\Polygon_ARE_C_dengan_holes_fixed_UPDATED.shp"
    
    print(f"Testing shapefile: {shapefile_path}")
    
    if not os.path.exists(shapefile_path):
        print(f"ERROR: Shapefile not found at {shapefile_path}")
        return None
    
    try:
        # Load the shapefile
        gdf = gpd.read_file(shapefile_path)
        
        print(f"✓ Successfully loaded shapefile")
        print(f"  - Number of polygons: {len(gdf)}")
        print(f"  - CRS: {gdf.crs}")
        print(f"  - Columns: {list(gdf.columns)}")
        print(f"  - Geometry types: {gdf.geometry.geom_type.value_counts().to_dict()}")
        
        # Show sample of data
        print("\nSample data (first 5 rows):")
        for col in gdf.columns:
            if col != 'geometry':
                print(f"  {col}: {gdf[col].head().tolist()}")
        
        return gdf
        
    except Exception as e:
        print(f"ERROR loading shapefile: {e}")
        return None

def test_dissolve_operation(gdf, field_name=None, test_values=None):
    """Test the dissolve operation"""
    if gdf is None:
        print("Cannot test dissolve - no data loaded")
        return
    
    # Find a suitable field for testing
    if field_name is None:
        # Look for common field names
        possible_fields = ['BLOCK_ID', 'BLOCK', 'ID', 'NAME', 'DIVISI', 'SUB_DIVISI']
        field_name = None
        
        for field in possible_fields:
            if field in gdf.columns:
                field_name = field
                break
        
        if field_name is None:
            # Use the first non-geometry column
            non_geom_cols = [col for col in gdf.columns if col != 'geometry']
            if non_geom_cols:
                field_name = non_geom_cols[0]
            else:
                print("No suitable field found for testing")
                return
    
    print(f"\nTesting dissolve operation with field: {field_name}")
    
    # Get unique values
    unique_values = gdf[field_name].unique()
    unique_values = [str(val) for val in unique_values if pd.notna(val)]
    
    print(f"Unique values in {field_name}: {unique_values[:10]}...")  # Show first 10
    
    # Use test values or select a few for testing
    if test_values is None:
        test_values = unique_values[:3]  # Test with first 3 values
    
    print(f"Testing with values: {test_values}")
    
    try:
        # Filter data
        mask = gdf[field_name].astype(str).isin(test_values)
        filtered_gdf = gdf[mask].copy()
        
        print(f"Filtered to {len(filtered_gdf)} polygons")
        
        if len(filtered_gdf) == 0:
            print("No polygons found with test values")
            return
        
        # Perform dissolve
        print("Performing dissolve operation...")
        dissolved = filtered_gdf.dissolve(by=field_name, as_index=False)
        
        print(f"✓ Dissolve successful!")
        print(f"  - Original polygons: {len(filtered_gdf)}")
        print(f"  - Dissolved features: {len(dissolved)}")
        
        # Show results
        for _, row in dissolved.iterrows():
            value = row[field_name]
            geom_type = type(row.geometry).__name__
            area = row.geometry.area if hasattr(row.geometry, 'area') else 0
            print(f"  - {value}: {geom_type}, Area = {area:.2f}")
        
        return dissolved
        
    except Exception as e:
        print(f"ERROR during dissolve operation: {e}")
        return None

def main():
    """Main test function"""
    print("=== Polygon Dissolve Test ===\n")
    
    # Test loading
    gdf = test_shapefile_loading()
    
    if gdf is not None:
        # Test dissolve
        dissolved = test_dissolve_operation(gdf)
        
        if dissolved is not None:
            print("\n✓ All tests passed! The dissolve functionality should work correctly.")
        else:
            print("\n✗ Dissolve test failed.")
    else:
        print("\n✗ Shapefile loading failed.")

if __name__ == "__main__":
    main()
