import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class PolygonDissolveApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Polygon Dissolve Tool - Merge Polygons by Field Value")
        self.root.geometry("1200x800")
        
        # Data storage
        self.gdf = None
        self.dissolved_gdf = None
        self.input_shapefile_path = ""
        self.output_shapefile_path = ""
        
        # Default input path
        self.default_input_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\Polygon_ARE_C_dengan_holes_fixed_UPDATED.shp"
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Polygon Dissolve Tool", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Input file selection
        ttk.Label(main_frame, text="Input Shapefile:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.input_path_var = tk.StringVar(value=self.default_input_path)
        input_entry = ttk.Entry(main_frame, textvariable=self.input_path_var, width=60)
        input_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="Browse", 
                  command=self.browse_input_file).grid(row=1, column=2, pady=5)
        
        # Load button
        ttk.Button(main_frame, text="Load Shapefile", 
                  command=self.load_shapefile).grid(row=2, column=0, columnspan=3, pady=10)
        
        # Separator
        ttk.Separator(main_frame, orient='horizontal').grid(row=3, column=0, columnspan=3, 
                                                           sticky=(tk.W, tk.E), pady=10)
        
        # Field selection frame
        field_frame = ttk.LabelFrame(main_frame, text="Field Selection", padding="10")
        field_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        field_frame.columnconfigure(1, weight=1)
        
        ttk.Label(field_frame, text="Grouping Field:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.field_var = tk.StringVar()
        self.field_combo = ttk.Combobox(field_frame, textvariable=self.field_var, 
                                       state="readonly", width=30)
        self.field_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        self.field_combo.bind('<<ComboboxSelected>>', self.on_field_selected)
        
        # Value selection frame
        value_frame = ttk.LabelFrame(main_frame, text="Value Selection", padding="10")
        value_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        value_frame.columnconfigure(0, weight=1)
        
        # Values listbox with scrollbar
        values_list_frame = ttk.Frame(value_frame)
        values_list_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        values_list_frame.columnconfigure(0, weight=1)
        
        self.values_listbox = tk.Listbox(values_list_frame, selectmode=tk.EXTENDED, height=6)
        values_scrollbar = ttk.Scrollbar(values_list_frame, orient=tk.VERTICAL, 
                                        command=self.values_listbox.yview)
        self.values_listbox.configure(yscrollcommand=values_scrollbar.set)
        
        self.values_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        values_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Selection buttons
        button_frame = ttk.Frame(value_frame)
        button_frame.grid(row=1, column=0, columnspan=2, pady=5)
        
        ttk.Button(button_frame, text="Select All", 
                  command=self.select_all_values).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear Selection", 
                  command=self.clear_value_selection).pack(side=tk.LEFT, padx=5)
        
        # Process button
        ttk.Button(main_frame, text="Dissolve Polygons", 
                  command=self.dissolve_polygons).grid(row=6, column=0, columnspan=3, pady=15)
        
        # Output file selection
        output_frame = ttk.LabelFrame(main_frame, text="Output", padding="10")
        output_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="Output Shapefile:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.output_path_var = tk.StringVar()
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, width=60)
        output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(output_frame, text="Browse", 
                  command=self.browse_output_file).grid(row=0, column=2, pady=5)
        
        ttk.Button(output_frame, text="Save Dissolved Shapefile", 
                  command=self.save_dissolved_shapefile).grid(row=1, column=0, columnspan=3, pady=10)
        
        # Status and info frame
        info_frame = ttk.LabelFrame(main_frame, text="Information", padding="10")
        info_frame.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        info_frame.columnconfigure(0, weight=1)
        
        self.info_text = tk.Text(info_frame, height=8, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        info_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Configure row weights for resizing
        main_frame.rowconfigure(8, weight=1)
        info_frame.rowconfigure(0, weight=1)
        
        self.log_message("Application started. Please load a shapefile to begin.")

    def log_message(self, message):
        """Add a message to the info text area"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.info_text.see(tk.END)
        self.root.update_idletasks()

    def browse_input_file(self):
        """Browse for input shapefile"""
        filename = filedialog.askopenfilename(
            title="Select Input Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")],
            initialdir=os.path.dirname(self.default_input_path) if os.path.exists(os.path.dirname(self.default_input_path)) else "."
        )
        if filename:
            self.input_path_var.set(filename)

    def browse_output_file(self):
        """Browse for output shapefile location"""
        filename = filedialog.asksaveasfilename(
            title="Save Dissolved Shapefile As",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")],
            defaultextension=".shp",
            initialdir=os.path.dirname(self.input_path_var.get()) if self.input_path_var.get() else "."
        )
        if filename:
            self.output_path_var.set(filename)

    def load_shapefile(self):
        """Load the input shapefile and analyze its structure"""
        input_path = self.input_path_var.get().strip()

        if not input_path:
            messagebox.showerror("Error", "Please select an input shapefile.")
            return

        if not os.path.exists(input_path):
            messagebox.showerror("Error", f"File not found: {input_path}")
            return

        try:
            self.log_message(f"Loading shapefile: {os.path.basename(input_path)}")

            # Load the shapefile
            self.gdf = gpd.read_file(input_path)
            self.input_shapefile_path = input_path

            # Log basic information
            self.log_message(f"Loaded {len(self.gdf)} polygons")
            self.log_message(f"CRS: {self.gdf.crs}")
            self.log_message(f"Columns: {list(self.gdf.columns)}")

            # Populate field combobox (exclude geometry column)
            fields = [col for col in self.gdf.columns if col != 'geometry']
            self.field_combo['values'] = fields

            if fields:
                self.field_combo.set(fields[0])  # Set first field as default
                self.on_field_selected()  # Populate values

            # Set default output path
            if not self.output_path_var.get():
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_dir = os.path.dirname(input_path)
                default_output = os.path.join(output_dir, f"{base_name}_dissolved.shp")
                self.output_path_var.set(default_output)

            self.log_message("Shapefile loaded successfully!")

        except Exception as e:
            error_msg = f"Error loading shapefile: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def on_field_selected(self, event=None):
        """Handle field selection change"""
        if self.gdf is None:
            return

        selected_field = self.field_var.get()
        if not selected_field:
            return

        try:
            # Get unique values for the selected field
            unique_values = self.gdf[selected_field].unique()
            unique_values = [str(val) for val in unique_values if pd.notna(val)]
            unique_values.sort()

            # Clear and populate the listbox
            self.values_listbox.delete(0, tk.END)
            for value in unique_values:
                self.values_listbox.insert(tk.END, value)

            # Log information about the field
            self.log_message(f"Field '{selected_field}' has {len(unique_values)} unique values")

            # Show value counts
            value_counts = self.gdf[selected_field].value_counts()
            self.log_message("Value distribution:")
            for value, count in value_counts.head(10).items():
                self.log_message(f"  {value}: {count} polygons")
            if len(value_counts) > 10:
                self.log_message(f"  ... and {len(value_counts) - 10} more values")

        except Exception as e:
            error_msg = f"Error analyzing field '{selected_field}': {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def select_all_values(self):
        """Select all values in the listbox"""
        self.values_listbox.select_set(0, tk.END)

    def clear_value_selection(self):
        """Clear all selections in the listbox"""
        self.values_listbox.selection_clear(0, tk.END)

    def dissolve_polygons(self):
        """Dissolve polygons based on selected field and values"""
        if self.gdf is None:
            messagebox.showerror("Error", "Please load a shapefile first.")
            return

        selected_field = self.field_var.get()
        if not selected_field:
            messagebox.showerror("Error", "Please select a grouping field.")
            return

        # Get selected values
        selected_indices = self.values_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("Error", "Please select at least one value to dissolve.")
            return

        selected_values = [self.values_listbox.get(i) for i in selected_indices]

        try:
            self.log_message(f"Starting dissolve operation...")
            self.log_message(f"Field: {selected_field}")
            self.log_message(f"Selected values: {selected_values}")

            # Filter the GeoDataFrame to include only selected values
            mask = self.gdf[selected_field].astype(str).isin(selected_values)
            filtered_gdf = self.gdf[mask].copy()

            if len(filtered_gdf) == 0:
                messagebox.showerror("Error", "No polygons found with the selected values.")
                return

            self.log_message(f"Found {len(filtered_gdf)} polygons to dissolve")

            # Perform the dissolve operation
            # This will merge all polygons with the same field value into single features
            self.log_message("Performing dissolve operation...")
            dissolved = filtered_gdf.dissolve(by=selected_field, as_index=False)

            # Ensure we have valid geometries
            dissolved = dissolved[dissolved.geometry.is_valid].copy()

            # Convert any MultiPolygons to their outer boundary if needed
            self.log_message("Processing geometries to extract outer boundaries...")
            processed_geometries = []

            for idx, row in dissolved.iterrows():
                geom = row.geometry

                if isinstance(geom, MultiPolygon):
                    # For MultiPolygon, we want to create a single polygon from the outer boundary
                    # We'll use the convex hull or union of all parts
                    try:
                        # Try to get the union of all polygons (this removes internal boundaries)
                        unified_geom = geom.buffer(0)  # Fix any topology issues first
                        if isinstance(unified_geom, MultiPolygon):
                            # If still MultiPolygon, take the convex hull to get outer boundary
                            unified_geom = unified_geom.convex_hull
                        processed_geometries.append(unified_geom)
                    except Exception as e:
                        self.log_message(f"Warning: Error processing MultiPolygon for {row[selected_field]}: {e}")
                        processed_geometries.append(geom)

                elif isinstance(geom, Polygon):
                    # For single polygons, ensure they're valid
                    try:
                        if not geom.is_valid:
                            geom = geom.buffer(0)
                        processed_geometries.append(geom)
                    except Exception as e:
                        self.log_message(f"Warning: Error processing Polygon for {row[selected_field]}: {e}")
                        processed_geometries.append(geom)
                else:
                    processed_geometries.append(geom)

            # Update the dissolved GeoDataFrame with processed geometries
            dissolved.geometry = processed_geometries

            # Store the result
            self.dissolved_gdf = dissolved

            # Log results
            self.log_message(f"Dissolve completed successfully!")
            self.log_message(f"Original polygons: {len(filtered_gdf)}")
            self.log_message(f"Dissolved features: {len(dissolved)}")

            # Show summary of dissolved features
            for idx, row in dissolved.iterrows():
                value = row[selected_field]
                area = row.geometry.area if hasattr(row.geometry, 'area') else 0
                self.log_message(f"  {value}: Area = {area:.2f} square units")

            messagebox.showinfo("Success",
                              f"Dissolve operation completed!\n"
                              f"Created {len(dissolved)} dissolved features from {len(filtered_gdf)} original polygons.")

        except Exception as e:
            error_msg = f"Error during dissolve operation: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def save_dissolved_shapefile(self):
        """Save the dissolved shapefile"""
        if self.dissolved_gdf is None:
            messagebox.showerror("Error", "No dissolved data to save. Please run dissolve operation first.")
            return

        output_path = self.output_path_var.get().strip()
        if not output_path:
            messagebox.showerror("Error", "Please specify an output file path.")
            return

        try:
            # Ensure output directory exists
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            self.log_message(f"Saving dissolved shapefile to: {output_path}")

            # Save the dissolved GeoDataFrame
            self.dissolved_gdf.to_file(output_path)

            self.log_message("Shapefile saved successfully!")
            messagebox.showinfo("Success", f"Dissolved shapefile saved to:\n{output_path}")

        except Exception as e:
            error_msg = f"Error saving shapefile: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)
