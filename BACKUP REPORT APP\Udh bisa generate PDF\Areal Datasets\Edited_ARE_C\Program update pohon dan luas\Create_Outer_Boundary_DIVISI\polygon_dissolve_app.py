import os
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import geopandas as gpd
import pandas as pd
from shapely.geometry import Polygon, MultiPolygon
from shapely.validation import explain_validity, make_valid
from shapely.ops import unary_union
from shapely import affinity
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class PolygonDissolveApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Polygon Dissolve Tool - Merge Polygons by Field Value")
        self.root.geometry("1200x800")
        
        # Data storage
        self.gdf = None
        self.dissolved_gdf = None
        self.input_shapefile_path = ""
        self.output_shapefile_path = ""
        
        # Default input path
        self.default_input_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\Polygon_ARE_C_dengan_holes_fixed_UPDATED.shp"
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Polygon Dissolve Tool", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Input file selection
        ttk.Label(main_frame, text="Input Shapefile:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.input_path_var = tk.StringVar(value=self.default_input_path)
        input_entry = ttk.Entry(main_frame, textvariable=self.input_path_var, width=60)
        input_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(main_frame, text="Browse", 
                  command=self.browse_input_file).grid(row=1, column=2, pady=5)
        
        # Load button
        ttk.Button(main_frame, text="Load Shapefile", 
                  command=self.load_shapefile).grid(row=2, column=0, columnspan=3, pady=10)
        
        # Separator
        ttk.Separator(main_frame, orient='horizontal').grid(row=3, column=0, columnspan=3, 
                                                           sticky=(tk.W, tk.E), pady=10)
        
        # Field selection frame
        field_frame = ttk.LabelFrame(main_frame, text="Field Selection", padding="10")
        field_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        field_frame.columnconfigure(1, weight=1)
        
        ttk.Label(field_frame, text="Grouping Field:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.field_var = tk.StringVar()
        self.field_combo = ttk.Combobox(field_frame, textvariable=self.field_var, 
                                       state="readonly", width=30)
        self.field_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        self.field_combo.bind('<<ComboboxSelected>>', self.on_field_selected)
        
        # Value selection frame
        value_frame = ttk.LabelFrame(main_frame, text="Value Selection", padding="10")
        value_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        value_frame.columnconfigure(0, weight=1)
        
        # Values listbox with scrollbar
        values_list_frame = ttk.Frame(value_frame)
        values_list_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        values_list_frame.columnconfigure(0, weight=1)
        
        self.values_listbox = tk.Listbox(values_list_frame, selectmode=tk.EXTENDED, height=6)
        values_scrollbar = ttk.Scrollbar(values_list_frame, orient=tk.VERTICAL, 
                                        command=self.values_listbox.yview)
        self.values_listbox.configure(yscrollcommand=values_scrollbar.set)
        
        self.values_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        values_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Selection buttons
        button_frame = ttk.Frame(value_frame)
        button_frame.grid(row=1, column=0, columnspan=2, pady=5)
        
        ttk.Button(button_frame, text="Select All", 
                  command=self.select_all_values).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Clear Selection", 
                  command=self.clear_value_selection).pack(side=tk.LEFT, padx=5)
        
        # Process button
        ttk.Button(main_frame, text="Dissolve Polygons", 
                  command=self.dissolve_polygons).grid(row=6, column=0, columnspan=3, pady=15)
        
        # Output file selection
        output_frame = ttk.LabelFrame(main_frame, text="Output", padding="10")
        output_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="Output Shapefile:").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.output_path_var = tk.StringVar()
        output_entry = ttk.Entry(output_frame, textvariable=self.output_path_var, width=60)
        output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 5))
        ttk.Button(output_frame, text="Browse", 
                  command=self.browse_output_file).grid(row=0, column=2, pady=5)
        
        ttk.Button(output_frame, text="Save Dissolved Shapefile", 
                  command=self.save_dissolved_shapefile).grid(row=1, column=0, columnspan=3, pady=10)
        
        # Status and info frame
        info_frame = ttk.LabelFrame(main_frame, text="Information", padding="10")
        info_frame.grid(row=8, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        info_frame.columnconfigure(0, weight=1)
        
        self.info_text = tk.Text(info_frame, height=8, wrap=tk.WORD)
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        info_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Configure row weights for resizing
        main_frame.rowconfigure(8, weight=1)
        info_frame.rowconfigure(0, weight=1)
        
        self.log_message("Application started. Please load a shapefile to begin.")

    def log_message(self, message):
        """Add a message to the info text area"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.info_text.see(tk.END)
        self.root.update_idletasks()

    def browse_input_file(self):
        """Browse for input shapefile"""
        filename = filedialog.askopenfilename(
            title="Select Input Shapefile",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")],
            initialdir=os.path.dirname(self.default_input_path) if os.path.exists(os.path.dirname(self.default_input_path)) else "."
        )
        if filename:
            self.input_path_var.set(filename)

    def browse_output_file(self):
        """Browse for output shapefile location"""
        filename = filedialog.asksaveasfilename(
            title="Save Dissolved Shapefile As",
            filetypes=[("Shapefile", "*.shp"), ("All files", "*.*")],
            defaultextension=".shp",
            initialdir=os.path.dirname(self.input_path_var.get()) if self.input_path_var.get() else "."
        )
        if filename:
            self.output_path_var.set(filename)

    def load_shapefile(self):
        """Load the input shapefile and analyze its structure"""
        input_path = self.input_path_var.get().strip()

        if not input_path:
            messagebox.showerror("Error", "Please select an input shapefile.")
            return

        if not os.path.exists(input_path):
            messagebox.showerror("Error", f"File not found: {input_path}")
            return

        try:
            self.log_message(f"Loading shapefile: {os.path.basename(input_path)}")

            # Load the shapefile
            self.gdf = gpd.read_file(input_path)
            self.input_shapefile_path = input_path

            # Log basic information
            self.log_message(f"Loaded {len(self.gdf)} polygons")
            self.log_message(f"CRS: {self.gdf.crs}")
            self.log_message(f"Columns: {list(self.gdf.columns)}")

            # Populate field combobox (exclude geometry column)
            fields = [col for col in self.gdf.columns if col != 'geometry']
            self.field_combo['values'] = fields

            if fields:
                self.field_combo.set(fields[0])  # Set first field as default
                self.on_field_selected()  # Populate values

            # Set default output path
            if not self.output_path_var.get():
                base_name = os.path.splitext(os.path.basename(input_path))[0]
                output_dir = os.path.dirname(input_path)
                default_output = os.path.join(output_dir, f"{base_name}_dissolved.shp")
                self.output_path_var.set(default_output)

            self.log_message("Shapefile loaded successfully!")

        except Exception as e:
            error_msg = f"Error loading shapefile: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def on_field_selected(self, event=None):
        """Handle field selection change"""
        if self.gdf is None:
            return

        selected_field = self.field_var.get()
        if not selected_field:
            return

        try:
            # Get unique values for the selected field
            unique_values = self.gdf[selected_field].unique()
            unique_values = [str(val) for val in unique_values if pd.notna(val)]
            unique_values.sort()

            # Clear and populate the listbox
            self.values_listbox.delete(0, tk.END)
            for value in unique_values:
                self.values_listbox.insert(tk.END, value)

            # Log information about the field
            self.log_message(f"Field '{selected_field}' has {len(unique_values)} unique values")

            # Show value counts
            value_counts = self.gdf[selected_field].value_counts()
            self.log_message("Value distribution:")
            for value, count in value_counts.head(10).items():
                self.log_message(f"  {value}: {count} polygons")
            if len(value_counts) > 10:
                self.log_message(f"  ... and {len(value_counts) - 10} more values")

        except Exception as e:
            error_msg = f"Error analyzing field '{selected_field}': {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def select_all_values(self):
        """Select all values in the listbox"""
        self.values_listbox.select_set(0, tk.END)

    def clear_value_selection(self):
        """Clear all selections in the listbox"""
        self.values_listbox.selection_clear(0, tk.END)

    def validate_and_repair_geometry(self, geometry, index=None):
        """
        Validate and repair invalid geometries using multiple strategies

        Args:
            geometry: Shapely geometry object
            index: Optional index for logging purposes

        Returns:
            tuple: (repaired_geometry, repair_method_used)
        """
        if geometry is None or geometry.is_empty:
            return None, "empty_geometry"

        # Check if geometry is already valid
        if geometry.is_valid:
            return geometry, "already_valid"

        # Log the validity issue
        validity_reason = explain_validity(geometry)
        log_prefix = f"Polygon {index}: " if index is not None else "Geometry: "
        self.log_message(f"{log_prefix}Invalid - {validity_reason}")

        # Strategy 1: Use Shapely's make_valid (most robust)
        try:
            repaired = make_valid(geometry)
            if repaired.is_valid and not repaired.is_empty:
                self.log_message(f"{log_prefix}Repaired using make_valid()")
                return repaired, "make_valid"
        except Exception as e:
            self.log_message(f"{log_prefix}make_valid() failed: {e}")

        # Strategy 2: Buffer with zero distance
        try:
            repaired = geometry.buffer(0)
            if repaired.is_valid and not repaired.is_empty:
                self.log_message(f"{log_prefix}Repaired using buffer(0)")
                return repaired, "buffer_zero"
        except Exception as e:
            self.log_message(f"{log_prefix}buffer(0) failed: {e}")

        # Strategy 3: Small positive buffer followed by negative buffer
        try:
            small_buffer = 1e-10  # Very small buffer
            repaired = geometry.buffer(small_buffer).buffer(-small_buffer)
            if repaired.is_valid and not repaired.is_empty:
                self.log_message(f"{log_prefix}Repaired using small buffer technique")
                return repaired, "small_buffer"
        except Exception as e:
            self.log_message(f"{log_prefix}Small buffer technique failed: {e}")

        # Strategy 4: For Polygons, try to rebuild from exterior ring
        if isinstance(geometry, Polygon):
            try:
                exterior_coords = list(geometry.exterior.coords)
                if len(exterior_coords) >= 4:  # Minimum for a valid polygon
                    repaired = Polygon(exterior_coords)
                    if repaired.is_valid and not repaired.is_empty:
                        self.log_message(f"{log_prefix}Repaired by rebuilding from exterior ring")
                        return repaired, "rebuild_exterior"
            except Exception as e:
                self.log_message(f"{log_prefix}Exterior ring rebuild failed: {e}")

        # Strategy 5: For MultiPolygons, try to extract valid parts
        if isinstance(geometry, MultiPolygon):
            try:
                valid_parts = []
                for geom in geometry.geoms:
                    if geom.is_valid and not geom.is_empty:
                        valid_parts.append(geom)
                    else:
                        # Try to repair individual parts
                        repaired_part, _ = self.validate_and_repair_geometry(geom)
                        if repaired_part and repaired_part.is_valid and not repaired_part.is_empty:
                            valid_parts.append(repaired_part)

                if valid_parts:
                    if len(valid_parts) == 1:
                        repaired = valid_parts[0]
                    else:
                        repaired = MultiPolygon(valid_parts)

                    if repaired.is_valid and not repaired.is_empty:
                        self.log_message(f"{log_prefix}Repaired by extracting valid parts from MultiPolygon")
                        return repaired, "extract_valid_parts"
            except Exception as e:
                self.log_message(f"{log_prefix}MultiPolygon part extraction failed: {e}")

        # Strategy 6: Convex hull as last resort
        try:
            repaired = geometry.convex_hull
            if repaired.is_valid and not repaired.is_empty:
                self.log_message(f"{log_prefix}Repaired using convex hull (shape approximated)")
                return repaired, "convex_hull"
        except Exception as e:
            self.log_message(f"{log_prefix}Convex hull failed: {e}")

        # If all strategies fail
        self.log_message(f"{log_prefix}All repair strategies failed - geometry will be skipped")
        return None, "repair_failed"

    def validate_and_repair_geodataframe(self, gdf):
        """
        Validate and repair all geometries in a GeoDataFrame

        Args:
            gdf: GeoDataFrame to validate and repair

        Returns:
            GeoDataFrame: Cleaned GeoDataFrame with valid geometries
        """
        self.log_message("Starting geometry validation and repair...")

        original_count = len(gdf)
        invalid_count = 0
        repaired_count = 0
        failed_count = 0
        repair_methods = {}

        # Create a copy to work with
        cleaned_gdf = gdf.copy()
        geometries_to_drop = []

        for idx, row in cleaned_gdf.iterrows():
            geometry = row.geometry

            if geometry is None or geometry.is_empty:
                geometries_to_drop.append(idx)
                failed_count += 1
                continue

            if not geometry.is_valid:
                invalid_count += 1
                repaired_geom, repair_method = self.validate_and_repair_geometry(geometry, idx)

                if repaired_geom is not None:
                    cleaned_gdf.at[idx, 'geometry'] = repaired_geom
                    repaired_count += 1
                    repair_methods[repair_method] = repair_methods.get(repair_method, 0) + 1
                else:
                    geometries_to_drop.append(idx)
                    failed_count += 1

        # Drop geometries that couldn't be repaired
        if geometries_to_drop:
            cleaned_gdf = cleaned_gdf.drop(geometries_to_drop)

        # Log summary
        self.log_message(f"Geometry validation complete:")
        self.log_message(f"  Original polygons: {original_count}")
        self.log_message(f"  Invalid geometries found: {invalid_count}")
        self.log_message(f"  Successfully repaired: {repaired_count}")
        self.log_message(f"  Failed to repair (dropped): {failed_count}")
        self.log_message(f"  Final valid polygons: {len(cleaned_gdf)}")

        if repair_methods:
            self.log_message("Repair methods used:")
            for method, count in repair_methods.items():
                self.log_message(f"  {method}: {count} polygons")

        return cleaned_gdf

    def dissolve_polygons(self):
        """Dissolve polygons based on selected field and values"""
        if self.gdf is None:
            messagebox.showerror("Error", "Please load a shapefile first.")
            return

        selected_field = self.field_var.get()
        if not selected_field:
            messagebox.showerror("Error", "Please select a grouping field.")
            return

        # Get selected values
        selected_indices = self.values_listbox.curselection()
        if not selected_indices:
            messagebox.showerror("Error", "Please select at least one value to dissolve.")
            return

        selected_values = [self.values_listbox.get(i) for i in selected_indices]

        try:
            self.log_message(f"Starting dissolve operation...")
            self.log_message(f"Field: {selected_field}")
            self.log_message(f"Selected values: {selected_values}")

            # Filter the GeoDataFrame to include only selected values
            mask = self.gdf[selected_field].astype(str).isin(selected_values)
            filtered_gdf = self.gdf[mask].copy()

            if len(filtered_gdf) == 0:
                messagebox.showerror("Error", "No polygons found with the selected values.")
                return

            self.log_message(f"Found {len(filtered_gdf)} polygons to dissolve")

            # Validate and repair geometries before dissolving
            self.log_message("Validating and repairing geometries...")
            cleaned_gdf = self.validate_and_repair_geodataframe(filtered_gdf)

            if len(cleaned_gdf) == 0:
                messagebox.showerror("Error", "No valid geometries remaining after repair attempts.")
                return

            self.log_message(f"Proceeding with {len(cleaned_gdf)} valid polygons")

            # Perform the dissolve operation with error handling
            # This will merge all polygons with the same field value into single features
            self.log_message("Performing dissolve operation...")

            try:
                dissolved = cleaned_gdf.dissolve(by=selected_field, as_index=False)
            except Exception as e:
                # If dissolve still fails, try alternative approach
                self.log_message(f"Standard dissolve failed: {e}")
                self.log_message("Attempting alternative dissolve method...")

                try:
                    # Group by field value and use unary_union for each group
                    dissolved_list = []

                    for value in selected_values:
                        value_mask = cleaned_gdf[selected_field].astype(str) == value
                        value_gdf = cleaned_gdf[value_mask]

                        if len(value_gdf) == 0:
                            continue

                        self.log_message(f"Processing {len(value_gdf)} polygons for value '{value}'")

                        # Get all geometries for this value
                        geometries = value_gdf.geometry.tolist()

                        # Use unary_union to merge them
                        try:
                            merged_geom = unary_union(geometries)

                            # Create a new row with the merged geometry
                            new_row = value_gdf.iloc[0].copy()  # Use first row as template
                            new_row.geometry = merged_geom
                            new_row[selected_field] = value

                            dissolved_list.append(new_row)

                        except Exception as union_error:
                            self.log_message(f"Failed to merge polygons for value '{value}': {union_error}")
                            continue

                    if dissolved_list:
                        dissolved = gpd.GeoDataFrame(dissolved_list, crs=cleaned_gdf.crs)
                        self.log_message("Alternative dissolve method succeeded")
                    else:
                        raise Exception("All dissolve methods failed")

                except Exception as alt_error:
                    error_msg = f"All dissolve methods failed: {alt_error}"
                    self.log_message(error_msg)
                    messagebox.showerror("Error", error_msg)
                    return

            # Final validation and processing of dissolved geometries
            self.log_message("Processing and validating dissolved geometries...")

            # Ensure all dissolved geometries are valid
            final_geometries = []
            final_rows = []

            for idx, row in dissolved.iterrows():
                geom = row.geometry
                field_value = row[selected_field]

                if geom is None or geom.is_empty:
                    self.log_message(f"Warning: Empty geometry for {field_value} - skipping")
                    continue

                # Validate and repair the dissolved geometry if needed
                if not geom.is_valid:
                    self.log_message(f"Dissolved geometry for {field_value} is invalid - attempting repair")
                    repaired_geom, repair_method = self.validate_and_repair_geometry(geom)

                    if repaired_geom is not None:
                        geom = repaired_geom
                        self.log_message(f"Repaired dissolved geometry for {field_value} using {repair_method}")
                    else:
                        self.log_message(f"Warning: Could not repair dissolved geometry for {field_value} - skipping")
                        continue

                # Process MultiPolygons to create cleaner boundaries if requested
                try:
                    if isinstance(geom, MultiPolygon):
                        # For MultiPolygon, we can either keep as-is or create outer boundary
                        # Let's keep the MultiPolygon but ensure all parts are valid
                        valid_parts = []
                        for part in geom.geoms:
                            if part.is_valid and not part.is_empty:
                                valid_parts.append(part)

                        if valid_parts:
                            if len(valid_parts) == 1:
                                geom = valid_parts[0]
                            else:
                                geom = MultiPolygon(valid_parts)
                        else:
                            self.log_message(f"Warning: No valid parts in MultiPolygon for {field_value} - skipping")
                            continue

                    # Final validation
                    if geom.is_valid and not geom.is_empty:
                        final_geometries.append(geom)
                        final_rows.append(row)
                    else:
                        self.log_message(f"Warning: Final geometry validation failed for {field_value} - skipping")

                except Exception as e:
                    self.log_message(f"Warning: Error processing geometry for {field_value}: {e} - skipping")
                    continue

            if not final_geometries:
                messagebox.showerror("Error", "No valid dissolved geometries could be created.")
                return

            # Create the final dissolved GeoDataFrame
            dissolved = gpd.GeoDataFrame(final_rows, crs=dissolved.crs)
            dissolved.geometry = final_geometries

            # Store the result
            self.dissolved_gdf = dissolved

            # Log results
            self.log_message(f"Dissolve completed successfully!")
            self.log_message(f"Original polygons: {len(filtered_gdf)}")
            self.log_message(f"Dissolved features: {len(dissolved)}")

            # Show summary of dissolved features
            for idx, row in dissolved.iterrows():
                value = row[selected_field]
                area = row.geometry.area if hasattr(row.geometry, 'area') else 0
                self.log_message(f"  {value}: Area = {area:.2f} square units")

            messagebox.showinfo("Success",
                              f"Dissolve operation completed!\n"
                              f"Created {len(dissolved)} dissolved features from {len(filtered_gdf)} original polygons.")

        except Exception as e:
            error_msg = f"Error during dissolve operation: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

    def save_dissolved_shapefile(self):
        """Save the dissolved shapefile"""
        if self.dissolved_gdf is None:
            messagebox.showerror("Error", "No dissolved data to save. Please run dissolve operation first.")
            return

        output_path = self.output_path_var.get().strip()
        if not output_path:
            messagebox.showerror("Error", "Please specify an output file path.")
            return

        try:
            # Ensure output directory exists
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            self.log_message(f"Saving dissolved shapefile to: {output_path}")

            # Save the dissolved GeoDataFrame
            self.dissolved_gdf.to_file(output_path)

            self.log_message("Shapefile saved successfully!")
            messagebox.showinfo("Success", f"Dissolved shapefile saved to:\n{output_path}")

        except Exception as e:
            error_msg = f"Error saving shapefile: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("Error", error_msg)

def main():
    """Main function to run the application"""
    root = tk.Tk()
    app = PolygonDissolveApp(root)

    # Center the window on screen
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    root.mainloop()

if __name__ == "__main__":
    main()
