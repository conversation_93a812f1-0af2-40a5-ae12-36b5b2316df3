# Polygon Dissolve Tool

A Python application for merging/dissolving multiple polygon blocks in shapefiles based on specified field values, keeping only the outer boundary of merged areas.

## Features

- **Interactive Field Selection**: Choose which field to use for grouping polygons
- **Flexible Value Selection**: Select specific field values to process or process all values
- **Polygon Dissolving**: Merge polygons with the same field value into single features
- **Outer Boundary Extraction**: Remove internal boundaries between merged polygons
- **Robust Geometry Repair**: Automatically detects and fixes invalid geometries using multiple strategies
- **TopologyException Handling**: Prevents crashes from common geometry issues like self-intersections
- **User-Friendly GUI**: Easy-to-use graphical interface built with Tkinter
- **Detailed Progress Logging**: Real-time feedback on processing status and geometry repairs

## Requirements

- Python 3.8 or higher
- Required packages (install via `pip install -r requirements_dissolve.txt`):
  - geopandas>=0.13.0
  - pandas>=1.5.0
  - shapely>=2.0.0
  - matplotlib>=3.5.0
  - numpy>=1.21.0
  - fiona>=1.8.0
  - pyproj>=3.3.0

## Installation

1. Clone or download this repository
2. Install the required dependencies:
   ```bash
   pip install -r requirements_dissolve.txt
   ```

## Usage

### Running the Application

```bash
python polygon_dissolve_app.py
```

### Step-by-Step Guide

1. **Load Shapefile**:
   - The application defaults to the specified input shapefile path
   - Click "Browse" to select a different shapefile
   - Click "Load Shapefile" to analyze the file

2. **Select Grouping Field**:
   - Choose the field that contains the values you want to group by
   - The application will show all available fields (excluding geometry)

3. **Select Values to Process**:
   - The listbox shows all unique values in the selected field
   - Select one or more values to dissolve
   - Use "Select All" to process all values
   - Use "Clear Selection" to deselect all values

4. **Dissolve Polygons**:
   - Click "Dissolve Polygons" to start the merging process
   - The application will merge all polygons with the same field value
   - Progress and results are shown in the information panel

5. **Save Results**:
   - Specify an output file path or use the default
   - Click "Save Dissolved Shapefile" to save the results

## Example Scenario

If your shapefile has polygons with a field "BLOCK_ID" containing values like "C1", "C2", "C3":

- **Input**: Multiple polygons with BLOCK_ID = "C1", "C2", "C3"
- **Process**: Select BLOCK_ID as grouping field, select values "C1", "C2", "C3"
- **Output**: 3 dissolved polygon features (one for each block ID)
- **Result**: Internal boundaries between polygons of the same block are removed

## Technical Details

### Dissolve Process

1. **Filtering**: Only polygons with selected field values are processed
2. **Geometry Validation**: All geometries are validated and repaired before dissolving
3. **Geometry Repair Strategies** (applied in order):
   - `make_valid()`: Shapely's robust geometry repair function
   - `buffer(0)`: Zero-distance buffer to fix topology issues
   - Small buffer technique: Positive then negative buffer
   - Exterior ring rebuild: Reconstruct polygon from outer ring
   - Valid parts extraction: Extract valid parts from MultiPolygons
   - Convex hull: Last resort approximation
4. **Dissolving**: GeoPandas `dissolve()` function merges polygons by field value
5. **Alternative Dissolve**: If standard dissolve fails, uses `unary_union` per group
6. **Final Validation**: Ensures all output geometries are valid

### Output Format

- Same CRS (Coordinate Reference System) as input
- All original attributes preserved for dissolved features
- Geometry column contains the merged polygon boundaries

## Default Configuration

- **Input Path**: `D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\Polygon_ARE_C_dengan_holes_fixed_UPDATED.shp`
- **Output Path**: Same directory as input with "_dissolved" suffix

## Troubleshooting

### Common Issues

1. **"No polygons found"**: Check that selected values exist in the chosen field
2. **TopologyException errors**: Now automatically handled with geometry repair
3. **Invalid geometry warnings**: Check the log for details on which geometries were repaired
4. **Memory issues**: For very large shapefiles, consider processing smaller subsets
5. **Some polygons dropped**: Invalid geometries that couldn't be repaired are logged and excluded

### Error Logging

All operations are logged in the information panel with timestamps. Error messages provide specific details about any issues encountered.

## Geometry Repair Features

The application includes comprehensive geometry validation and repair capabilities to handle common issues in real-world shapefile data:

### Automatic Detection
- Validates all geometries before processing
- Identifies specific validity issues using Shapely's `explain_validity()`
- Logs detailed information about each invalid geometry found

### Repair Strategies
The application attempts multiple repair methods in sequence:

1. **make_valid()**: Uses Shapely's most robust repair function
2. **buffer(0)**: Applies zero-distance buffer to fix topology issues
3. **Small buffer technique**: Applies tiny positive then negative buffer
4. **Exterior ring rebuild**: Reconstructs polygons from their outer boundary
5. **Valid parts extraction**: Extracts valid components from MultiPolygons
6. **Convex hull**: Creates simplified outer boundary as last resort

### Logging and Reporting
- Reports total number of invalid geometries found
- Shows which repair method was successful for each geometry
- Counts how many geometries were successfully repaired vs. dropped
- Provides summary statistics of the repair process

### Fallback Mechanisms
- If standard `dissolve()` fails, attempts alternative `unary_union` approach
- Validates all final geometries before saving
- Ensures no invalid geometries in the output

## File Structure

```
Create_Outer_Boundary_DIVISI/
├── polygon_dissolve_app.py      # Main application
├── requirements_dissolve.txt    # Python dependencies
└── README_polygon_dissolve.md   # This documentation
```

## License

This tool is part of the Tree Counting Project and is intended for internal use in processing palm oil plantation data.
