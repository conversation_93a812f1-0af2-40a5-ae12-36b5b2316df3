#!/usr/bin/env python3
"""
Simple test to check if the shapefile exists and basic Python functionality works
"""

import os
import sys

def test_basic_functionality():
    """Test basic Python functionality"""
    print("=== Basic Functionality Test ===")
    
    # Test file path
    shapefile_path = r"D:\Gawean Rebinmas\Tree Counting Project\Information System Web Tree Counted\Assets\shapefile\Are C\Polygon_ARE_C_dengan_holes_fixed_UPDATED.shp"
    
    print(f"Target shapefile: {shapefile_path}")
    
    if os.path.exists(shapefile_path):
        print("✓ Shapefile exists")
        
        # Get file info
        file_size = os.path.getsize(shapefile_path)
        print(f"  File size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
        
        # Check for associated files
        base_path = os.path.splitext(shapefile_path)[0]
        associated_files = ['.shx', '.dbf', '.prj', '.cpg']
        
        print("  Associated files:")
        for ext in associated_files:
            assoc_file = base_path + ext
            if os.path.exists(assoc_file):
                print(f"    ✓ {ext} file exists")
            else:
                print(f"    ✗ {ext} file missing")
        
        return True
    else:
        print("✗ Shapefile not found")
        return False

def test_imports():
    """Test if required packages can be imported"""
    print("\n=== Package Import Test ===")
    
    packages = [
        ('os', 'Built-in'),
        ('sys', 'Built-in'),
        ('tkinter', 'GUI framework'),
        ('pandas', 'Data manipulation'),
        ('numpy', 'Numerical computing'),
        ('geopandas', 'Geospatial data'),
        ('shapely', 'Geometric operations'),
        ('matplotlib', 'Plotting'),
        ('fiona', 'File I/O')
    ]
    
    success_count = 0
    for package, description in packages:
        try:
            __import__(package)
            print(f"✓ {package} - {description}")
            success_count += 1
        except ImportError as e:
            print(f"✗ {package} - {description} (Error: {e})")
    
    print(f"\nImport results: {success_count}/{len(packages)} packages available")
    return success_count

def main():
    """Main test function"""
    print("Polygon Dissolve Tool - Environment Test")
    print("=" * 50)
    
    # Test basic functionality
    shapefile_exists = test_basic_functionality()
    
    # Test imports
    import_count = test_imports()
    
    print("\n" + "=" * 50)
    print("SUMMARY:")
    
    if shapefile_exists:
        print("✓ Target shapefile is accessible")
    else:
        print("✗ Target shapefile is not accessible")
    
    if import_count >= 6:  # At least basic packages
        print("✓ Most required packages are available")
        print("  You should be able to run the polygon dissolve tool")
    elif import_count >= 3:
        print("⚠ Some packages are missing")
        print("  Run: python install_dependencies.py")
    else:
        print("✗ Many required packages are missing")
        print("  Please install Python packages first")
    
    print("\nNext steps:")
    if shapefile_exists and import_count >= 6:
        print("1. Run: python polygon_dissolve_app.py")
        print("2. Load the shapefile and test the dissolve functionality")
    else:
        if not shapefile_exists:
            print("1. Verify the shapefile path is correct")
        if import_count < 6:
            print("2. Install missing packages: python install_dependencies.py")
        print("3. Re-run this test: python simple_dissolve_test.py")

if __name__ == "__main__":
    main()
