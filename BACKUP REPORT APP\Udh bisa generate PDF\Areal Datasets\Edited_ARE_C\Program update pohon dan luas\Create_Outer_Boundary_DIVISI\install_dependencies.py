#!/usr/bin/env python3
"""
Installation script for polygon dissolve dependencies
"""

import subprocess
import sys

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package}: {e}")
        return False

def main():
    """Install required packages"""
    packages = [
        "geopandas>=0.13.0",
        "pandas>=1.5.0", 
        "shapely>=2.0.0",
        "matplotlib>=3.5.0",
        "numpy>=1.21.0",
        "fiona>=1.8.0",
        "pyproj>=3.3.0"
    ]
    
    print("Installing dependencies for Polygon Dissolve Tool...")
    print("=" * 50)
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print("=" * 50)
    print(f"Installation complete: {success_count}/{len(packages)} packages installed successfully")
    
    if success_count == len(packages):
        print("✓ All dependencies installed successfully!")
        print("You can now run: python polygon_dissolve_app.py")
    else:
        print("✗ Some packages failed to install. Please check the errors above.")

if __name__ == "__main__":
    main()
